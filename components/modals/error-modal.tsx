/**
 * Reusable Error Modal Component
 *
 * Usage examples:
 *
 * 1. With Sumsub error:
 * <ErrorModal
 *   isVisible={showError}
 *   error={sumsubError}
 *   onClose={() => setShowError(false)}
 *   onRetry={handleRetry}
 *   title="Verification Error"
 * />
 *
 * 2. With custom message:
 * <ErrorModal
 *   isVisible={showError}
 *   error={null}
 *   onClose={() => setShowError(false)}
 *   title="Upload Failed"
 *   message="Failed to upload your document. Please try again."
 *   helpText="Make sure your file is under 10MB and in a supported format."
 *   onRetry={handleUploadRetry}
 * />
 *
 * 3. Simple error without retry:
 * <ErrorModal
 *   isVisible={showError}
 *   error={null}
 *   onClose={() => setShowError(false)}
 *   title="Network Error"
 *   message="Please check your internet connection."
 * />
 */

import React from "react";
import { StyleSheet, View } from "react-native";
import Modal from "react-native-modal";
import { Ionicons } from "@expo/vector-icons";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { SumsubError } from "@/utils/sumsub-error-handler";
import { AppError } from "@/utils/error-handler";

interface ErrorModalProps {
  isVisible: boolean;
  error: SumsubError | AppError | null;
  onClose: () => void;
  onRetry?: () => void;
  title?: string;
  message?: string;
  helpText?: string;
}

export const ErrorModal = ({
  isVisible,
  error,
  onClose,
  onRetry,
  title = "Something went wrong",
  message,
  helpText,
}: ErrorModalProps) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    onClose();
  };

  // Determine if error is retryable and get retry action text
  const isRetryable = error?.retryable ?? false;
  const retryActionText = (error as SumsubError)?.retryAction || "Try Again";

  // Get error message and help text (use props if provided, otherwise from error object)
  const errorMessage =
    message || error?.userFriendlyMessage || "An unexpected error occurred.";
  const errorHelpText = helpText || (error as SumsubError)?.helpText;

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      onBackButtonPress={onClose}
      animationIn="fadeIn"
      animationOut="fadeOut"
    >
      <View style={styles.modalContent}>
        <View style={styles.errorModalHeader}>
          <Ionicons
            name="alert-circle"
            size={scale(32)}
            color="#DC2626"
            style={styles.errorModalIcon}
          />
          <AppText size="lg" weight="bold" style={styles.errorModalTitle}>
            {title}
          </AppText>
        </View>

        <AppText style={styles.errorModalMessage}>{errorMessage}</AppText>

        {errorHelpText && (
          <AppText style={styles.errorModalHelpText}>{errorHelpText}</AppText>
        )}

        <View style={styles.errorModalActions}>
          {isRetryable && onRetry && (
            <AppButton
              text={retryActionText}
              variant="default"
              size="sm"
              onPress={handleRetry}
            />
          )}
          <AppButton text="Close" variant="ghost" size="sm" onPress={onClose} />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(24),
    width: "100%",
    maxWidth: scale(400),
    alignSelf: "center",
  },
  errorModalHeader: {
    alignItems: "center",
    marginBottom: verticalScale(16),
  },
  errorModalIcon: {
    marginBottom: verticalScale(8),
  },
  errorModalTitle: {
    textAlign: "center",
    color: "#DC2626",
  },
  errorModalMessage: {
    textAlign: "center",
    marginBottom: verticalScale(12),
    fontSize: scale(16),
    lineHeight: verticalScale(22),
    color: COLORS.text,
  },
  errorModalHelpText: {
    textAlign: "center",
    marginBottom: verticalScale(16),
    fontSize: scale(14),
    lineHeight: verticalScale(20),
    color: COLORS.gray,
  },
  errorModalActions: {
    gap: verticalScale(12),
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
