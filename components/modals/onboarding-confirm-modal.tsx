import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";
import { RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import Modal from "react-native-modal";
import {
  launchSNSMobileSDK,
  runLivenessCheckProcess,
} from "@/services/sumsub/launchSNSMobileSDK";
import { getLivenessCheckToken } from "@/services/sumsub/sumsub-service";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useCreateUserCompanyConsent } from "@/services/company/company-hooks";
import { useRefetchUserData } from "@/hooks/useRefetchUserData";
import {
  SumsubError,
  analyzeSumsubError,
  logSumsubError,
} from "@/utils/sumsub-error-handler";
import { ErrorModal } from "./error-modal";

interface OnboardingConfirmModalProps {
  companyName: string;
  companyLogo?: string;
  companyTierNumber: number;
  companyId: number;
}

export const OnboardingConfirmModal = ({
  companyName,
  companyId,
}: OnboardingConfirmModalProps) => {
  const { refreshUserData } = useRefetchUserData();
  const [isVisible, setIsVisible] = useState(false);
  const [isLivenessLoading, setIsLivenessLoading] = useState(false);
  const [error, setError] = useState<SumsubError | null>(null);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const { data: user } = useCurrentUser();

  const isKyb = user?.registrationtype === RegistrationTypeOption.KYB;

  // Consent creation mutation
  const { mutate: createUserCompanyConsent, isPending } =
    useCreateUserCompanyConsent({
      onSuccess: () => {
        console.log("✅ Consent created successfully after liveness check");
        setError(null); // Clear any previous errors
        setIsVisible(false);
        refreshUserData();
      },
      onError: () => {
        console.error("❌ Failed to create consent");
        const sumsubError = analyzeSumsubError(
          new Error("Failed to create consent"),
          "consent_creation"
        );
        logSumsubError(
          sumsubError,
          "OnboardingConfirmModal - consent creation"
        );
        setError(sumsubError);
        setShowErrorModal(true);
      },
    });
  const onClose = () => {
    setIsVisible(false);
  };

  const closeModal = () => {
    setIsVisible(false);
  };

  const triggerLiveCheck = async () => {
    try {
      setIsLivenessLoading(true);
      setError(null); // Clear any previous errors

      if (!user?.email || !user?.user_id || !user?.applicant_id) {
        const sumsubError = analyzeSumsubError(
          new Error("Missing user information for onboarding"),
          "liveness_check"
        );
        logSumsubError(sumsubError, "OnboardingConfirmModal - user validation");
        setError(sumsubError);
        setShowErrorModal(true);
        return;
      }

      console.log("🎯 Starting onboarding liveness check for:", companyName);
      console.log(
        "👤 User type:",
        isKyb ? "KYB (Company)" : "KYC (Individual)"
      );

      // Step 1: Liveness check with automatic user type detection
      // This works for both KYC and KYB users, matching web implementation

      const tokenData = await getLivenessCheckToken();

      console.log(`✅ Liveness check token received: SUCCESS`);

      console.log(`🚀 Launching Sumsub SDK for liveness check...`);
      await launchSNSMobileSDK({
        sumSubData: {
          token: tokenData.token,
          userEmail: user.email,
        },
        tier: SUMSUB_LEVEL.LIVE,
        statusChangeHandler: (event) => {
          console.log(
            `📱 Liveness Status: ${event.prevStatus} → ${event.newStatus}`
          );
          if (event.newStatus === "Approved") {
            setError(null); // Clear errors on success
            setTimeout(() => {
              setIsVisible(true);
            }, 3000);
          } else if (
            event.newStatus === "Failed" ||
            event.newStatus === "FinallyRejected"
          ) {
            const sumsubError = analyzeSumsubError(
              new Error(`Liveness check ${event.newStatus.toLowerCase()}`),
              "liveness_check"
            );
            logSumsubError(
              sumsubError,
              "OnboardingConfirmModal - liveness status"
            );
            setError(sumsubError);
            setShowErrorModal(true);
          }
        },
      });
    } catch (error: any) {
      console.error("❌ Liveness check failed:", error);

      let sumsubError: SumsubError;
      if (error.sumsubType) {
        // Already a SumsubError
        sumsubError = error;
      } else {
        // Convert to SumsubError
        sumsubError = analyzeSumsubError(error, "liveness_check");
      }

      logSumsubError(sumsubError, "OnboardingConfirmModal - triggerLiveCheck");
      setError(sumsubError);
      setShowErrorModal(true);
    } finally {
      setIsLivenessLoading(false);
    }
  };

  const onConfirm = async () => {
    setError(null); // Clear any previous errors
    createUserCompanyConsent({
      userId: user?.user_id || "",
      companyId: companyId, // Already number type from props
      applicant_id: user?.applicant_id || "",
    });
  };

  const handleRetry = () => {
    setError(null);
    setShowErrorModal(false);
    if (isVisible) {
      // If modal is visible, retry consent creation
      onConfirm();
    } else {
      // If modal is not visible, retry liveness check
      triggerLiveCheck();
    }
  };

  const closeErrorModal = () => {
    setShowErrorModal(false);
    setError(null);
  };

  return (
    <View>
      <AppButton
        text={"ONBOARD"}
        size="lg"
        onPress={triggerLiveCheck}
        isLoading={isLivenessLoading}
        disabled={isLivenessLoading}
      />

      {/* Error Modal */}
      <ErrorModal
        isVisible={showErrorModal}
        error={error}
        onClose={closeErrorModal}
        onRetry={handleRetry}
        title="Verification Error"
      />

      <Modal
        isVisible={isVisible}
        onBackdropPress={closeModal}
        onSwipeComplete={closeModal}
        onBackButtonPress={closeModal}
      >
        <View style={styles.modalContent}>
          <AppText size="xl" weight="bold" style={styles.title}>
            Are you sure you want to apply to onboard with {companyName}?
          </AppText>

          <AppText style={styles.description}>
            By confirming, you are giving consent to share your personal
            identity information with
            <AppText weight="bold"> {companyName}</AppText>. You can revoke this
            consent at any time in your user profile page.
          </AppText>

          <View style={styles.actions}>
            <AppButton
              text="Confirm"
              variant="default"
              size="sm"
              onPress={onConfirm}
              isLoading={isPending}
              disabled={isPending}
            />
            <AppButton
              text="Cancel"
              variant="ghost"
              size="sm"
              onPress={onClose}
              disabled={isPending}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(24),
    width: "100%",
    maxWidth: scale(400),
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  description: {
    textAlign: "center",
    marginBottom: verticalScale(24),
    color: COLORS.gray,
  },
  actions: {
    gap: verticalScale(12),
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
