/**
 * Sumsub-specific error handling utilities
 * Extends the base error handling system for Sumsub operations
 */

import { ErrorType, createAppError, AppError, analyzeError } from './error-handler';

export enum SumsubErrorType {
  TOKEN_FETCH_FAILED = "TOKEN_FETCH_FAILED",
  LIVENESS_CHECK_FAILED = "LIVENESS_CHECK_FAILED",
  VERIFICATION_FAILED = "VERIFICATION_FAILED",
  BENEFICIARY_INFO_MISSING = "BENEFICIARY_INFO_MISSING",
  USER_INFO_MISSING = "USER_INFO_MISSING",
  SDK_LAUNCH_FAILED = "SDK_LAUNCH_FAILED",
  CONSENT_CREATION_FAILED = "CONSENT_CREATION_FAILED",
}

export interface SumsubError extends AppError {
  sumsubType: SumsubErrorType;
  retryAction?: string;
  helpText?: string;
}

/**
 * Creates a Sumsub-specific error with user-friendly messages and retry guidance
 */
export const createSumsubError = (
  sumsubType: SumsubErrorType,
  message: string,
  originalError?: any,
  code?: string | number
): SumsubError => {
  let baseErrorType: ErrorType;
  let userFriendlyMessage: string;
  let retryAction: string;
  let helpText: string;
  let retryable: boolean;

  switch (sumsubType) {
    case SumsubErrorType.TOKEN_FETCH_FAILED:
      baseErrorType = ErrorType.SERVER;
      userFriendlyMessage = "Unable to start verification process. Please try again.";
      retryAction = "Tap 'ONBOARD' to try again";
      helpText = "If this continues, please check your internet connection or contact support.";
      retryable = true;
      break;

    case SumsubErrorType.LIVENESS_CHECK_FAILED:
      baseErrorType = ErrorType.VALIDATION;
      userFriendlyMessage = "Identity verification failed. Please try the verification process again.";
      retryAction = "Tap 'ONBOARD' to restart verification";
      helpText = "Make sure you're in a well-lit area and follow the on-screen instructions carefully.";
      retryable = true;
      break;

    case SumsubErrorType.VERIFICATION_FAILED:
      baseErrorType = ErrorType.VALIDATION;
      userFriendlyMessage = "Verification could not be completed. Please try again.";
      retryAction = "Tap 'ONBOARD' to retry verification";
      helpText = "Ensure your documents are clear and you follow all instructions during the process.";
      retryable = true;
      break;

    case SumsubErrorType.BENEFICIARY_INFO_MISSING:
      baseErrorType = ErrorType.VALIDATION;
      userFriendlyMessage = "Company verification setup is incomplete. Please contact support.";
      retryAction = "Contact support for assistance";
      helpText = "Your company account needs additional setup before verification can proceed.";
      retryable = false;
      break;

    case SumsubErrorType.USER_INFO_MISSING:
      baseErrorType = ErrorType.VALIDATION;
      userFriendlyMessage = "User information is incomplete. Please log out and log back in.";
      retryAction = "Log out and log back in";
      helpText = "Your account information may need to be refreshed.";
      retryable = false;
      break;

    case SumsubErrorType.SDK_LAUNCH_FAILED:
      baseErrorType = ErrorType.UNKNOWN;
      userFriendlyMessage = "Verification system could not start. Please try again.";
      retryAction = "Tap 'ONBOARD' to try again";
      helpText = "If this continues, please restart the app or contact support.";
      retryable = true;
      break;

    case SumsubErrorType.CONSENT_CREATION_FAILED:
      baseErrorType = ErrorType.SERVER;
      userFriendlyMessage = "Unable to complete onboarding. Please try again.";
      retryAction = "Tap 'Confirm' to try again";
      helpText = "Your verification was successful, but we couldn't complete the final step.";
      retryable = true;
      break;

    default:
      baseErrorType = ErrorType.UNKNOWN;
      userFriendlyMessage = "An unexpected error occurred during verification.";
      retryAction = "Try again";
      helpText = "Please contact support if this continues.";
      retryable = true;
  }

  const baseError = createAppError(baseErrorType, message, originalError, code);

  return {
    ...baseError,
    sumsubType,
    userFriendlyMessage,
    retryAction,
    helpText,
    retryable,
  };
};

/**
 * Analyzes Sumsub-specific errors and creates appropriate error objects
 */
export const analyzeSumsubError = (error: any, context?: string): SumsubError => {
  // Check for specific Sumsub error patterns
  if (error?.message?.includes("Failed to fetch Sumsub token") || 
      error?.message?.includes("Failed to get Sumsub")) {
    return createSumsubError(
      SumsubErrorType.TOKEN_FETCH_FAILED,
      error.message,
      error
    );
  }

  if (error?.message?.includes("Missing user information") ||
      error?.message?.includes("User not found")) {
    return createSumsubError(
      SumsubErrorType.USER_INFO_MISSING,
      error.message,
      error
    );
  }

  if (error?.message?.includes("Missing beneficiary information")) {
    return createSumsubError(
      SumsubErrorType.BENEFICIARY_INFO_MISSING,
      error.message,
      error
    );
  }

  if (context === "liveness_check") {
    return createSumsubError(
      SumsubErrorType.LIVENESS_CHECK_FAILED,
      error.message || "Liveness check failed",
      error
    );
  }

  if (context === "sdk_launch") {
    return createSumsubError(
      SumsubErrorType.SDK_LAUNCH_FAILED,
      error.message || "SDK launch failed",
      error
    );
  }

  if (context === "consent_creation") {
    return createSumsubError(
      SumsubErrorType.CONSENT_CREATION_FAILED,
      error.message || "Consent creation failed",
      error
    );
  }

  // Fall back to general analysis
  const baseError = analyzeError(error);
  return createSumsubError(
    SumsubErrorType.VERIFICATION_FAILED,
    baseError.message,
    error,
    baseError.code
  );
};

/**
 * Retry mechanism specifically for Sumsub operations with exponential backoff
 */
export const withSumsubRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 2,
  initialDelay: number = 1500
): Promise<T> => {
  let lastError: any;
  let delay = initialDelay;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const sumsubError = analyzeSumsubError(error);

      // Don't retry non-retryable errors
      if (!sumsubError.retryable || attempt === maxRetries) {
        throw sumsubError;
      }

      console.log(`Sumsub operation attempt ${attempt} failed, retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
      delay *= 1.5; // Moderate exponential backoff for user experience
    }
  }

  throw analyzeSumsubError(lastError);
};

/**
 * Logs Sumsub errors with additional context
 */
export const logSumsubError = (error: SumsubError, context?: string) => {
  const logMessage = `[SUMSUB-${error.sumsubType}] ${context ? `${context}: ` : ""}${error.message}`;
  
  console.error(logMessage, {
    originalError: error.originalError,
    userFriendlyMessage: error.userFriendlyMessage,
    retryAction: error.retryAction,
    helpText: error.helpText,
    retryable: error.retryable,
  });
};
