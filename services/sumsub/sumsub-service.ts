import { Company, RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import { getCurrentToken } from "@/utils/token-utils";
import { getCurrentUser } from "../user/user-service";
import {
  createSumsubError,
  analyzeSumsubError,
  withSumsubRetry,
  logSumsubError,
  SumsubErrorType,
} from "@/utils/sumsub-error-handler";
import { withErrorHandling, ErrorHandlingResult } from "@/utils/error-handler";

// Type definitions for API response
interface SumsubApiResponse {
  data: string;
  errors: null | string;
  isSuccess: boolean;
  token?: string; // fallback property
}

/**
 * Convert tier number to SUMSUB_LEVEL enum based on registration type
 * @param tierNumber - Tier number (0, 1, 2, 3)
 * @param userRegistrationType - KYC (individual) or KYB (company)
 * @returns Corresponding SUMSUB_LEVEL enum value
 */
export const getTierSumsubLevel = (
  tierNumber: number,
  userRegistrationType: RegistrationTypeOption
): SUMSUB_LEVEL => {
  if (userRegistrationType === RegistrationTypeOption.KYB) {
    // KYB tiers
    switch (tierNumber) {
      case 0:
        return SUMSUB_LEVEL.KYB_TIER0;
      case 1:
        return SUMSUB_LEVEL.KYB_TIER1;
      case 2:
        return SUMSUB_LEVEL.KYB_TIER2;
      case 3:
        return SUMSUB_LEVEL.KYB_TIER3;
      default:
        return SUMSUB_LEVEL.KYB_TIER0;
    }
  } else {
    // KYC (individual) tiers
    switch (tierNumber) {
      case 0:
        return SUMSUB_LEVEL.TIER0;
      case 1:
        return SUMSUB_LEVEL.TIER1;
      case 2:
        return SUMSUB_LEVEL.TIER2;
      case 3:
        return SUMSUB_LEVEL.TIER3;
      default:
        return SUMSUB_LEVEL.TIER0;
    }
  }
};

/**
 * Get company tier information including number, name, and SUMSUB_LEVEL
 * @param company - Company object with tier requirements
 * @param userRegistrationType - KYC (individual) or KYB (company)
 * @returns Object with tier number, name, and SUMSUB_LEVEL enum
 */
export const getCompanyTier = ({
  company,
  userRegistrationType,
}: {
  company: Company;
  userRegistrationType: RegistrationTypeOption;
}) => {
  const companyTierNumber =
    userRegistrationType === RegistrationTypeOption.KYB
      ? company.required_corporate_tier
      : company.required_individual_tier;

  const companyTierSumsubLevel = getTierSumsubLevel(
    companyTierNumber,
    userRegistrationType
  );

  return {
    companyTierNumber,
    companyTierSumsubLevel,
  };
};

export const getSumsubAccessToken = async ({
  tierEnum,
}: {
  tierEnum: SUMSUB_LEVEL;
}): Promise<{
  token: string;
  userEmail: string | undefined;
}> => {
  return withSumsubRetry(async () => {
    const currentUser = await getCurrentUser();
    const userId = currentUser?.user_id;
    const token = await getCurrentToken();

    if (!currentUser || !userId) {
      throw createSumsubError(
        SumsubErrorType.USER_INFO_MISSING,
        "User information is missing for token request"
      );
    }

    const url = `https://api.profitecosystem.com/api/v1/Sumsub/get-tier-sdk-userid-level-token?userId=${userId}&levelName=${tierEnum}`;

    console.log("GET TOKEN URL ->", url);

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("RESPONSE STATUS ->", response.status);

      if (!response.ok) {
        throw createSumsubError(
          SumsubErrorType.TOKEN_FETCH_FAILED,
          `HTTP ${response.status}: Failed to fetch Sumsub token`,
          null,
          response.status
        );
      }

      const responseData: SumsubApiResponse = await response.json();
      console.log("NORMALIZED RESPONSE ->", responseData);

      // Parse the data string to get the actual token
      let sumsubToken;
      try {
        if (responseData.data && typeof responseData.data === "string") {
          const parsedData = JSON.parse(responseData.data);
          sumsubToken = parsedData.token;
        } else {
          sumsubToken = responseData.token; // fallback if structure is different
        }
      } catch (error) {
        console.log("ERROR PARSING DATA ->", error);
        sumsubToken = responseData.token; // fallback
      }

      console.log("SUMSUB TOKEN ->", sumsubToken);

      if (!sumsubToken) {
        throw createSumsubError(
          SumsubErrorType.TOKEN_FETCH_FAILED,
          "Token not found in response"
        );
      }

      return {
        token: sumsubToken,
        userEmail: currentUser?.email,
      };
    } catch (error: any) {
      if (error.sumsubType) {
        // Already a SumsubError, re-throw
        throw error;
      }

      // Analyze and convert to SumsubError
      const sumsubError = analyzeSumsubError(error, "token_fetch");
      logSumsubError(sumsubError, "getSumsubAccessToken");
      throw sumsubError;
    }
  });
};

/**
 * Get Sumsub token for company users with beneficiary information
 * Similar to web version's getClientSumSubToken function
 */
export const getSumsubAccessTokenForBeneficiary = async ({
  tierEnum,
  beneficiaryUserId,
  beneficiaryApplicantId,
}: {
  tierEnum: SUMSUB_LEVEL;
  beneficiaryUserId: string;
  beneficiaryApplicantId: string;
}): Promise<{
  token: string;
  userEmail: string | undefined;
}> => {
  return withSumsubRetry(async () => {
    const currentUser = await getCurrentUser();
    const token = await getCurrentToken();

    if (!beneficiaryUserId || !beneficiaryApplicantId) {
      throw createSumsubError(
        SumsubErrorType.BENEFICIARY_INFO_MISSING,
        "Beneficiary information is missing for token request"
      );
    }

    const url = `https://api.profitecosystem.com/api/v1/Sumsub/get-tier-sdk-userId-applicantId-level-token?userId=${beneficiaryUserId}&levelName=${tierEnum}&applicantId=${beneficiaryApplicantId}`;

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("BENEFICIARY RESPONSE STATUS ->", response.status);

      if (!response.ok) {
        throw createSumsubError(
          SumsubErrorType.TOKEN_FETCH_FAILED,
          `HTTP ${response.status}: Failed to fetch Sumsub beneficiary token`,
          null,
          response.status
        );
      }

      const responseData: SumsubApiResponse = await response.json();
      console.log("BENEFICIARY NORMALIZED RESPONSE ->", responseData);

      // Parse the data string to get the actual token
      let sumsubToken;
      try {
        if (responseData.data && typeof responseData.data === "string") {
          const parsedData = JSON.parse(responseData.data);
          sumsubToken = parsedData.token;
        } else {
          sumsubToken = responseData.token; // fallback if structure is different
        }
      } catch (error) {
        console.log("BENEFICIARY ERROR PARSING DATA ->", error);
        sumsubToken = responseData.token; // fallback
      }

      console.log("BENEFICIARY SUMSUB TOKEN ->", sumsubToken);

      if (!sumsubToken) {
        throw createSumsubError(
          SumsubErrorType.TOKEN_FETCH_FAILED,
          "Beneficiary token not found in response"
        );
      }

      return {
        token: sumsubToken,
        userEmail: currentUser?.email,
      };
    } catch (error: any) {
      if (error.sumsubType) {
        // Already a SumsubError, re-throw
        throw error;
      }

      // Analyze and convert to SumsubError
      const sumsubError = analyzeSumsubError(error, "beneficiary_token_fetch");
      logSumsubError(sumsubError, "getSumsubAccessTokenForBeneficiary");
      throw sumsubError;
    }
  });
};

/**
 * Get liveness check token with automatic user type detection
 * Similar to web version's fetchLivenessToken function
 * Now returns proper error information instead of null for better error handling
 */
export const getLivenessCheckToken = async (): Promise<{
  token: string;
  userEmail: string | undefined;
}> => {
  const result = await withErrorHandling(async () => {
    const currentUser = await getCurrentUser();

    console.log("Liveness - Current user:", currentUser);

    if (!currentUser) {
      throw createSumsubError(
        SumsubErrorType.USER_INFO_MISSING,
        "User not found for liveness check"
      );
    }

    console.log("Liveness - User type:", currentUser.registrationtype);

    // Check if user is company type and has beneficiary information
    const isCompanyUser = currentUser.registrationtype === "company";
    const beneficiaryUserId = currentUser.userCompany?.beneficiaryUserId;
    const beneficiaryApplicantId =
      currentUser.userCompany?.beneficiaryApplicantId;

    console.log("Liveness - Is company user:", isCompanyUser);
    console.log("Liveness - Beneficiary User ID:", beneficiaryUserId);
    console.log("Liveness - Beneficiary Applicant ID:", beneficiaryApplicantId);

    if (isCompanyUser) {
      if (!beneficiaryUserId || !beneficiaryApplicantId) {
        throw createSumsubError(
          SumsubErrorType.BENEFICIARY_INFO_MISSING,
          "Missing beneficiary information for company user liveness check"
        );
      }

      // For company users, use the beneficiary userId with specialized function
      console.log(
        "Liveness - Using beneficiary userId for company user:",
        beneficiaryUserId
      );
      return await getSumsubAccessTokenForBeneficiary({
        tierEnum: SUMSUB_LEVEL.LIVE,
        beneficiaryUserId: beneficiaryUserId,
        beneficiaryApplicantId: beneficiaryApplicantId,
      });
    } else {
      // For individual users or companies without beneficiary info, use regular token
      console.log("Liveness - Using regular token for individual user");
      return await getSumsubAccessToken({
        tierEnum: SUMSUB_LEVEL.LIVE,
      });
    }
  });

  if (!result.success || !result.data) {
    const error =
      result.error ||
      analyzeSumsubError(
        new Error("Unknown liveness check error"),
        "liveness_check"
      );
    logSumsubError(error as any, "getLivenessCheckToken");
    throw error;
  }

  return result.data;
};
