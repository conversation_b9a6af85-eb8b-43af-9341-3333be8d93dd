import { Company, RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import { getCurrentToken } from "@/utils/token-utils";
import { getCurrentUser } from "../user/user-service";

// Type definitions for API response
interface SumsubApiResponse {
  data: string;
  errors: null | string;
  isSuccess: boolean;
  token?: string; // fallback property
}

/**
 * Convert tier number to SUMSUB_LEVEL enum based on registration type
 * @param tierNumber - Tier number (0, 1, 2, 3)
 * @param userRegistrationType - KYC (individual) or KYB (company)
 * @returns Corresponding SUMSUB_LEVEL enum value
 */
export const getTierSumsubLevel = (
  tierNumber: number,
  userRegistrationType: RegistrationTypeOption
): SUMSUB_LEVEL => {
  if (userRegistrationType === RegistrationTypeOption.KYB) {
    // KYB tiers
    switch (tierNumber) {
      case 0:
        return SUMSUB_LEVEL.KYB_TIER0;
      case 1:
        return SUMSUB_LEVEL.KYB_TIER1;
      case 2:
        return SUMSUB_LEVEL.KYB_TIER2;
      case 3:
        return SUMSUB_LEVEL.KYB_TIER3;
      default:
        return SUMSUB_LEVEL.KYB_TIER0;
    }
  } else {
    // KYC (individual) tiers
    switch (tierNumber) {
      case 0:
        return SUMSUB_LEVEL.TIER0;
      case 1:
        return SUMSUB_LEVEL.TIER1;
      case 2:
        return SUMSUB_LEVEL.TIER2;
      case 3:
        return SUMSUB_LEVEL.TIER3;
      default:
        return SUMSUB_LEVEL.TIER0;
    }
  }
};

/**
 * Get company tier information including number, name, and SUMSUB_LEVEL
 * @param company - Company object with tier requirements
 * @param userRegistrationType - KYC (individual) or KYB (company)
 * @returns Object with tier number, name, and SUMSUB_LEVEL enum
 */
export const getCompanyTier = ({
  company,
  userRegistrationType,
}: {
  company: Company;
  userRegistrationType: RegistrationTypeOption;
}) => {
  const companyTierNumber =
    userRegistrationType === RegistrationTypeOption.KYB
      ? company.required_corporate_tier
      : company.required_individual_tier;

  const companyTierSumsubLevel = getTierSumsubLevel(
    companyTierNumber,
    userRegistrationType
  );

  return {
    companyTierNumber,
    companyTierSumsubLevel,
  };
};

export const getSumsubAccessToken = async ({
  tierEnum,
}: {
  tierEnum: SUMSUB_LEVEL;
}): Promise<{
  token: string;
  userEmail: string | undefined;
}> => {
  const currentUser = await getCurrentUser();
  const userId = currentUser?.user_id;
  const token = await getCurrentToken();

  const url = `https://api.profitecosystem.com/api/v1/Sumsub/get-tier-sdk-userid-level-token?userId=${userId}&levelName=${tierEnum}`;

  console.log("GET TOKEN URL ->", url);

  const response: SumsubApiResponse = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  })
    .then((res) => {
      console.log("RESPONSE STATUS ->", res.status);
      return res.json();
    })
    .catch((err) => {
      console.log("FETCH ERROR ->", err);
      throw new Error(`Failed to fetch Sumsub token: ${err.message}`);
    });

  console.log("NORMALIZED RESPONSE ->", response);

  // Parse the data string to get the actual token
  let sumsubToken;
  try {
    if (response.data && typeof response.data === "string") {
      const parsedData = JSON.parse(response.data);
      sumsubToken = parsedData.token;
    } else {
      sumsubToken = response.token; // fallback if structure is different
    }
  } catch (error) {
    console.log("ERROR PARSING DATA ->", error);
    sumsubToken = response.token; // fallback
  }

  console.log("SUMSUB TOKEN ->", sumsubToken);

  if (!sumsubToken) {
    throw new Error("Failed to get Sumsub access token");
  }

  return {
    token: sumsubToken,
    userEmail: currentUser?.email,
  };
};

/**
 * Get Sumsub token for company users with beneficiary information
 * Similar to web version's getClientSumSubToken function
 */
export const getSumsubAccessTokenForBeneficiary = async ({
  tierEnum,
  beneficiaryUserId,
  beneficiaryApplicantId,
}: {
  tierEnum: SUMSUB_LEVEL;
  beneficiaryUserId: string;
  beneficiaryApplicantId: string;
}): Promise<{
  token: string;
  userEmail: string | undefined;
}> => {
  const currentUser = await getCurrentUser();
  const token = await getCurrentToken();

  const url = `https://api.profitecosystem.com/api/v1/Sumsub/get-tier-sdk-userId-applicantId-level-token?userId=${beneficiaryUserId}&levelName=${tierEnum}&applicantId=${beneficiaryApplicantId}`;

  const response: SumsubApiResponse = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  })
    .then((res) => {
      console.log("BENEFICIARY RESPONSE STATUS ->", res.status);
      return res.json();
    })
    .catch((err) => {
      console.log("BENEFICIARY FETCH ERROR ->", err);
      throw new Error(
        `Failed to fetch Sumsub beneficiary token: ${err.message}`
      );
    });

  console.log("BENEFICIARY NORMALIZED RESPONSE ->", response);

  // Parse the data string to get the actual token
  let sumsubToken;
  try {
    if (response.data && typeof response.data === "string") {
      const parsedData = JSON.parse(response.data);
      sumsubToken = parsedData.token;
    } else {
      sumsubToken = response.token; // fallback if structure is different
    }
  } catch (error) {
    console.log("BENEFICIARY ERROR PARSING DATA ->", error);
    sumsubToken = response.token; // fallback
  }

  console.log("BENEFICIARY SUMSUB TOKEN ->", sumsubToken);

  if (!sumsubToken) {
    throw new Error("Failed to get Sumsub beneficiary access token");
  }

  return {
    token: sumsubToken,
    userEmail: currentUser?.email,
  };
};

/**
 * Get liveness check token with automatic user type detection
 * Similar to web version's fetchLivenessToken function
 * Returns null if token cannot be obtained (for fallback handling)
 */
export const getLivenessCheckToken = async (): Promise<{
  token: string;
  userEmail: string | undefined;
} | null> => {
  try {
    const currentUser = await getCurrentUser();

    console.log("Liveness - Current user:", currentUser);

    if (!currentUser) {
      console.error("Liveness - User not found");
      return null;
    }

    console.log("Liveness - User type:", currentUser.registrationtype);

    // Check if user is company type and has beneficiary information
    const isCompanyUser = currentUser.registrationtype === "company";
    const beneficiaryUserId = currentUser.userCompany?.beneficiaryUserId;
    const beneficiaryApplicantId =
      currentUser.userCompany?.beneficiaryApplicantId;

    console.log("Liveness - Is company user:", isCompanyUser);
    console.log("Liveness - Beneficiary User ID:", beneficiaryUserId);
    console.log("Liveness - Beneficiary Applicant ID:", beneficiaryApplicantId);

    if (isCompanyUser) {
      if (!beneficiaryUserId || !beneficiaryApplicantId) {
        console.error("Liveness - Missing beneficiary information");
        return null;
      }

      // For company users, use the beneficiary userId with specialized function
      console.log(
        "Liveness - Using beneficiary userId for company user:",
        beneficiaryUserId
      );
      return await getSumsubAccessTokenForBeneficiary({
        tierEnum: SUMSUB_LEVEL.LIVE,
        beneficiaryUserId: beneficiaryUserId || "",
        beneficiaryApplicantId: beneficiaryApplicantId || "",
      });
    } else {
      // For individual users or companies without beneficiary info, use regular token
      console.log("Liveness - Using regular token for individual user");
      return await getSumsubAccessToken({
        tierEnum: SUMSUB_LEVEL.LIVE,
      });
    }
  } catch (error) {
    console.error("Liveness - Failed to get token:", error);
    return null; // Return null instead of throwing to allow fallback
  }
};
